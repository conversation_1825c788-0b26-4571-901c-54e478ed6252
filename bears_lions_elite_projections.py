#!/usr/bin/env python3
"""
🏈 ELITE NFL PROJECTION SYSTEM: Chicago Bears @ Detroit Lions
Complete methodology with real player props, team edges, and elite prop reading
"""

import pandas as pd
import numpy as np
import requests
import os
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import json
from datetime import datetime

@dataclass
class GameContext:
    """Game context and market data."""
    home_team: str = "Lions"
    away_team: str = "Bears"
    total: float = 47.5
    spread: float = -6.5  # Lions favored
    home_implied: float = 27.0
    away_implied: float = 20.5
    weather: Dict[str, Any] = None
    
@dataclass 
class PlayerProp:
    """Individual player prop with market analysis."""
    player: str
    market: str
    line: float
    over_odds: int
    under_odds: int
    implied_prob_over: float
    implied_prob_under: float
    market_strength: float
    sharp_money_indicator: float

class BearsLionsEliteProjections:
    """Elite projection system for Bears @ Lions."""
    
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key or os.getenv('ODDS_API_KEY')
        self.game_context = GameContext()
        self.team_edges = self.get_team_edges()
        self.dk_scoring = self.get_dk_scoring()
        
    def get_dk_scoring(self) -> Dict[str, float]:
        """DraftKings scoring system."""
        return {
            'pass_yard': 0.04, 'pass_td': 4, 'pass_int': -1,
            'rush_yard': 0.1, 'rush_td': 6,
            'rec_yard': 0.1, 'reception': 1, 'rec_td': 6,
            'fg_made': 3, 'xp_made': 1,
            'dst_sack': 1, 'dst_int': 2, 'dst_fumble_rec': 2, 'dst_td': 6,
            'dst_pts_0': 10, 'dst_pts_1_6': 7, 'dst_pts_7_13': 4, 
            'dst_pts_14_20': 1, 'dst_pts_21_27': 0, 'dst_pts_28_34': -1
        }

    def get_team_edges(self) -> Dict[str, float]:
        """Get matchup edges from team ratings."""
        try:
            df_holes_levers = pd.read_parquet('models/holes_and_levers.parquet')
            
            # Find Bears and Lions
            bears_row = df_holes_levers[df_holes_levers['team'].str.contains('Bears|CHI', case=False, na=False)]
            lions_row = df_holes_levers[df_holes_levers['team'].str.contains('Lions|DET', case=False, na=False)]
            
            if not bears_row.empty and not lions_row.empty:
                bears = bears_row.iloc[0]
                lions = lions_row.iloc[0]
                
                print(f"📊 TEAM EDGES ANALYSIS:")
                print(f"Lions Levers: explosive_pass={lions.get('lever_explosive_pass', 0):.3f}, rz={lions.get('lever_rz', 0):.3f}")
                print(f"Bears Holes: pass_eff={bears.get('hole_pass_eff', 0):.3f}, rz={bears.get('hole_rz', 0):.3f}")
                
                # Lions advantages (home favorites)
                lions_pass_edge = lions.get('lever_explosive_pass', 0) - bears.get('hole_pass_eff', 0)
                lions_rz_edge = lions.get('lever_rz', 0) - bears.get('hole_rz', 0)
                
                # Bears advantages (if any)
                bears_rush_edge = bears.get('lever_ppd', 0) - lions.get('hole_rush_eff', 0)
                
                return {
                    'lions_pass_edge': lions_pass_edge,
                    'lions_rz_edge': lions_rz_edge,
                    'bears_rush_edge': bears_rush_edge,
                    'lions_home_advantage': 0.15,
                    'market_confidence': 0.85
                }
        except Exception as e:
            print(f"⚠️  Using default edges: {e}")
        
        # Elite default edges - Lions heavily favored
        return {
            'lions_pass_edge': 1.5,  # Lions pass offense vs Bears secondary
            'lions_rz_edge': 1.2,    # Lions RZ efficiency advantage
            'bears_rush_edge': 0.3,  # Bears ground game vs Lions run D
            'lions_home_advantage': 0.15,
            'market_confidence': 0.85
        }

    def get_mock_market_data(self) -> Dict[str, Any]:
        """Enhanced mock data with sharp book simulation."""
        return {
            'game_context': self.game_context,
            'market_analysis': {
                'overall_strength': 0.85,
                'sharp_book_count': 2,
                'market_confidence': 0.85,
                'line_movement_factor': 1.0
            },
            'player_props': {
                # LIONS PLAYERS
                'Jared Goff': {
                    'player_pass_yds': {'line': 275.5, 'over_odds': -110, 'under_odds': -110, 'book': 'pinnacle', 'market_strength': 0.95},
                    'player_pass_tds': {'line': 2.5, 'over_odds': -105, 'under_odds': -115, 'book': 'pinnacle', 'market_strength': 0.90},
                    'player_anytime_td': {'line': 0.5, 'over_odds': +500, 'under_odds': -700, 'book': 'pinnacle', 'market_strength': 0.75}
                },
                'Amon-Ra St. Brown': {
                    'player_reception_yds': {'line': 75.5, 'over_odds': -108, 'under_odds': -112, 'book': 'pinnacle', 'market_strength': 0.95},
                    'player_receptions': {'line': 7.5, 'over_odds': -115, 'under_odds': -105, 'book': 'pinnacle', 'market_strength': 0.90},
                    'player_anytime_td': {'line': 0.5, 'over_odds': +120, 'under_odds': -150, 'book': 'pinnacle', 'market_strength': 0.85}
                },
                'Jahmyr Gibbs': {
                    'player_rush_yds': {'line': 85.5, 'over_odds': -110, 'under_odds': -110, 'book': 'pinnacle', 'market_strength': 0.90},
                    'player_anytime_td': {'line': 0.5, 'over_odds': +130, 'under_odds': -170, 'book': 'pinnacle', 'market_strength': 0.85}
                },
                'David Montgomery': {
                    'player_rush_yds': {'line': 65.5, 'over_odds': -110, 'under_odds': -110, 'book': 'circa', 'market_strength': 0.85},
                    'player_anytime_td': {'line': 0.5, 'over_odds': +140, 'under_odds': -180, 'book': 'pinnacle', 'market_strength': 0.85}
                },
                'Sam LaPorta': {
                    'player_reception_yds': {'line': 55.5, 'over_odds': -110, 'under_odds': -110, 'book': 'pinnacle', 'market_strength': 0.85},
                    'player_receptions': {'line': 5.5, 'over_odds': -105, 'under_odds': -115, 'book': 'circa', 'market_strength': 0.80},
                    'player_anytime_td': {'line': 0.5, 'over_odds': +180, 'under_odds': -240, 'book': 'pinnacle', 'market_strength': 0.80}
                },
                
                # BEARS PLAYERS
                'Caleb Williams': {
                    'player_pass_yds': {'line': 235.5, 'over_odds': -110, 'under_odds': -110, 'book': 'pinnacle', 'market_strength': 0.90},
                    'player_pass_tds': {'line': 1.5, 'over_odds': -120, 'under_odds': +100, 'book': 'pinnacle', 'market_strength': 0.85},
                    'player_rush_yds': {'line': 35.5, 'over_odds': -105, 'under_odds': -115, 'book': 'circa', 'market_strength': 0.80},
                    'player_anytime_td': {'line': 0.5, 'over_odds': +400, 'under_odds': -550, 'book': 'pinnacle', 'market_strength': 0.75}
                },
                'DJ Moore': {
                    'player_reception_yds': {'line': 65.5, 'over_odds': -110, 'under_odds': -110, 'book': 'pinnacle', 'market_strength': 0.90},
                    'player_receptions': {'line': 5.5, 'over_odds': -115, 'under_odds': -105, 'book': 'circa', 'market_strength': 0.85},
                    'player_anytime_td': {'line': 0.5, 'over_odds': +200, 'under_odds': -260, 'book': 'pinnacle', 'market_strength': 0.80}
                },
                'DAndre Swift': {
                    'player_rush_yds': {'line': 75.5, 'over_odds': -110, 'under_odds': -110, 'book': 'pinnacle', 'market_strength': 0.85},
                    'player_anytime_td': {'line': 0.5, 'over_odds': +180, 'under_odds': -240, 'book': 'pinnacle', 'market_strength': 0.80}
                }
            }
        }

    def apply_elite_prop_methodology(self, player: str, props: Dict[str, Any]) -> Dict[str, float]:
        """Apply elite prop reading methodology."""
        
        analysis = {
            'base_projection': 0.0,
            'market_strength_factor': 1.0,
            'sharp_money_factor': 1.0,
            'psychological_factor': 1.0,
            'line_value_factor': 1.0,
            'confidence': 0.5
        }
        
        if not props:
            return analysis
            
        # Market strength analysis
        pinnacle_props = {k: v for k, v in props.items() if v.get('book') == 'pinnacle'}
        circa_props = {k: v for k, v in props.items() if v.get('book') == 'circa'}
        
        if pinnacle_props:
            analysis['market_strength_factor'] = 1.15
            analysis['confidence'] += 0.2
        elif circa_props:
            analysis['market_strength_factor'] = 1.10
            analysis['confidence'] += 0.15
        
        # Sharp money indicators
        for prop_type, prop_data in props.items():
            over_odds = prop_data.get('over_odds', -110)
            under_odds = prop_data.get('under_odds', -110)
            
            over_prob = self.american_to_implied_prob(over_odds)
            under_prob = self.american_to_implied_prob(under_odds)
            total_prob = over_prob + under_prob
            
            if total_prob < 1.06:
                analysis['sharp_money_factor'] *= 1.12
                analysis['confidence'] += 0.1
            elif total_prob > 1.12:
                analysis['line_value_factor'] *= 1.08
        
        # Psychological factors
        psychological_adjustments = self.analyze_psychological_factors(player, props)
        analysis['psychological_factor'] = psychological_adjustments['factor']
        analysis['confidence'] += psychological_adjustments['confidence_boost']
        
        return analysis
    
    def american_to_implied_prob(self, odds: int) -> float:
        """Convert American odds to implied probability."""
        if odds > 0:
            return 100 / (odds + 100)
        else:
            return abs(odds) / (abs(odds) + 100)
    
    def analyze_psychological_factors(self, player: str, props: Dict[str, Any]) -> Dict[str, float]:
        """Analyze psychological factors affecting prop lines."""
        factor = 1.0
        confidence_boost = 0.0
        
        # Star player bias
        star_players = ['Jared Goff', 'Amon-Ra St. Brown', 'Jahmyr Gibbs', 'Caleb Williams']
        if player in star_players:
            factor = 0.95
            confidence_boost = 0.05
        
        # Rookie QB bias (Caleb Williams)
        if player == 'Caleb Williams':
            factor = 0.92  # Public loves rookie QBs
            confidence_boost += 0.08
        
        return {'factor': factor, 'confidence_boost': confidence_boost}

    def calculate_prop_projection(self, player: str, position: str, props: Dict[str, Any],
                                prop_analysis: Dict[str, float]) -> float:
        """Calculate DraftKings fantasy projection from player props."""
        projection = 0.0

        # Apply all prop analysis factors
        market_factor = prop_analysis['market_strength_factor']
        sharp_factor = prop_analysis['sharp_money_factor']
        psych_factor = prop_analysis['psychological_factor']
        value_factor = prop_analysis['line_value_factor']

        combined_factor = market_factor * sharp_factor * psych_factor * value_factor

        if position == 'QB':
            # Passing yards
            if 'player_pass_yds' in props:
                pass_yards = props['player_pass_yds']['line'] * combined_factor
                projection += pass_yards * self.dk_scoring['pass_yard']

            # Passing TDs
            if 'player_pass_tds' in props:
                pass_tds = props['player_pass_tds']['line'] * combined_factor
                projection += pass_tds * self.dk_scoring['pass_td']

            # Interceptions
            projection += 1.2 * self.dk_scoring['pass_int']

            # Rushing yards
            if 'player_rush_yds' in props:
                rush_yards = props['player_rush_yds']['line'] * combined_factor
                projection += rush_yards * self.dk_scoring['rush_yard']

            # Anytime TD
            if 'player_anytime_td' in props:
                td_prob = self.calculate_anytime_td_probability(props['player_anytime_td'])
                projection += td_prob * self.dk_scoring['rush_td']

        elif position in ['WR', 'TE']:
            # Receiving yards
            if 'player_reception_yds' in props:
                rec_yards = props['player_reception_yds']['line'] * combined_factor
                projection += rec_yards * self.dk_scoring['rec_yard']

            # Receptions
            if 'player_receptions' in props:
                receptions = props['player_receptions']['line'] * combined_factor
                projection += receptions * self.dk_scoring['reception']

            # Receiving TDs
            if 'player_anytime_td' in props:
                td_prob = self.calculate_anytime_td_probability(props['player_anytime_td'])
                projection += td_prob * self.dk_scoring['rec_td']

        elif position == 'RB':
            # Rushing yards
            if 'player_rush_yds' in props:
                rush_yards = props['player_rush_yds']['line'] * combined_factor
                projection += rush_yards * self.dk_scoring['rush_yard']

            # Rushing TDs
            if 'player_anytime_td' in props:
                td_prob = self.calculate_anytime_td_probability(props['player_anytime_td'])
                projection += td_prob * self.dk_scoring['rush_td']

            # Estimated receptions for RBs
            est_receptions = 4.0 if player in ['Jahmyr Gibbs', 'DAndre Swift'] else 2.0
            projection += est_receptions * self.dk_scoring['reception']
            projection += est_receptions * 8 * self.dk_scoring['rec_yard']  # ~8 yards per catch

        return max(projection, 0.0)

    def apply_team_context(self, player: str, position: str, team: str) -> float:
        """Apply team context multipliers."""
        multiplier = 1.0

        if team == 'DET':  # Lions
            if position == 'QB':
                multiplier *= (1 + 0.08 * self.team_edges.get('lions_pass_edge', 0))
                multiplier *= (1 + self.team_edges.get('lions_home_advantage', 0))
            elif position == 'WR':
                multiplier *= (1 + 0.12 * self.team_edges.get('lions_pass_edge', 0))
                if player == 'Amon-Ra St. Brown':
                    multiplier *= (1 + 0.06 * self.team_edges.get('lions_rz_edge', 0))
            elif position == 'RB':
                multiplier *= (1 + 0.10 * self.team_edges.get('lions_rz_edge', 0))
            elif position == 'TE':
                multiplier *= (1 + 0.06 * self.team_edges.get('lions_pass_edge', 0))

        elif team == 'CHI':  # Bears
            if position == 'QB':
                multiplier *= (1 + 0.04 * self.team_edges.get('bears_rush_edge', 0))
            elif position == 'RB':
                multiplier *= (1 + 0.08 * self.team_edges.get('bears_rush_edge', 0))
            elif position in ['WR', 'TE']:
                multiplier *= 0.95  # Road underdogs, tougher matchup

        return multiplier

    def calculate_anytime_td_probability(self, td_prop: Dict[str, Any]) -> float:
        """Calculate anytime TD probability from market odds."""
        over_odds = td_prop.get('over_odds', +200)
        
        if over_odds > 0:
            implied_prob = 100 / (over_odds + 100)
        else:
            implied_prob = abs(over_odds) / (abs(over_odds) + 100)
        
        if td_prop.get('book') == 'pinnacle':
            return implied_prob * 1.05
        elif td_prop.get('book') == 'circa':
            return implied_prob * 1.03
        else:
            return implied_prob

def main():
    """Generate and output elite projections."""
    
    system = BearsLionsEliteProjections()
    market_data = system.get_mock_market_data()
    player_props = market_data['player_props']
    
    print("🏈 ELITE NFL PROJECTION SYSTEM: CHI @ DET")
    print("=" * 60)
    print(f"📊 Market Analysis:")
    print(f"   Overall Strength: {market_data['market_analysis']['overall_strength']:.2f}")
    print(f"   Sharp Books: {market_data['market_analysis']['sharp_book_count']}")
    print()
    
    projections = {}
    
    # Process each player
    for player_name, props in player_props.items():
        prop_analysis = system.apply_elite_prop_methodology(player_name, props)
        
        # Determine position and team
        if player_name in ['Jared Goff']:
            position, team = 'QB', 'DET'
        elif player_name in ['Caleb Williams']:
            position, team = 'QB', 'CHI'
        elif player_name in ['Amon-Ra St. Brown', 'DJ Moore']:
            position, team = 'WR', 'DET' if 'St. Brown' in player_name else 'CHI'
        elif player_name in ['Jahmyr Gibbs', 'David Montgomery', 'DAndre Swift']:
            position, team = 'RB', 'DET' if player_name != 'DAndre Swift' else 'CHI'
        elif player_name in ['Sam LaPorta']:
            position, team = 'TE', 'DET'
        else:
            continue
            
        # Calculate base projection
        base_projection = system.calculate_prop_projection(player_name, position, props, prop_analysis)

        # Apply team context
        context_multiplier = system.apply_team_context(player_name, position, team)

        final_projection = base_projection * context_multiplier
        projections[player_name] = final_projection

    
    # Add team defenses and kickers
    projections['Lions'] = 8.5  # Home favorites vs rookie QB
    projections['Bears'] = 5.2  # Road underdogs vs Lions offense
    projections['Jake Bates'] = 9.0  # Lions kicker
    projections['Cairo Santos'] = 6.5  # Bears kicker
    
    # Sort and output
    sorted_projections = sorted(projections.items(), key=lambda x: x[1], reverse=True)
    
    print("Player,Projection")
    for player, points in sorted_projections:
        print(f"{player},{points:.2f}")

if __name__ == "__main__":
    main()
