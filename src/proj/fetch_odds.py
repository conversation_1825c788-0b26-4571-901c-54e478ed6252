"""Fetch NFL odds from The Odds API and other data providers."""

import os
import requests
import pandas as pd
from typing import Dict, Any, List
from dotenv import load_dotenv
from requests.auth import HTTPBasicAuth

load_dotenv()


def get_totals_spreads(api_key: str, sport: str = "americanfootball_nfl") -> Dict[str, Any]:
    """
    Fetch NFL spreads and totals from The Odds API.
    
    Args:
        api_key: The Odds API key
        sport: Sport identifier (default: americanfootball_nfl)
        
    Returns:
        Dictionary containing odds data
    """
    url = f"https://api.the-odds-api.com/v4/sports/{sport}/odds"
    
    params = {
        'api_key': api_key,
        'regions': 'us',
        'markets': 'spreads,totals',
        'oddsFormat': 'american',
        'dateFormat': 'iso'
    }
    
    response = requests.get(url, params=params)
    response.raise_for_status()
    
    return response.json()


def implied_team_totals(odds_data: Dict[str, Any]) -> pd.DataFrame:
    """
    Convert odds data to DataFrame with implied team totals.
    
    Args:
        odds_data: Raw odds data from The Odds API
        
    Returns:
        DataFrame with columns: home_team, away_team, spread, total, 
                               home_implied_total, away_implied_total
    """
    games = []
    
    for game in odds_data:
        home_team = game['home_team']
        away_team = game['away_team']
        commence_time = game['commence_time']
        
        # Initialize game data
        game_data = {
            'home_team': home_team,
            'away_team': away_team,
            'commence_time': commence_time,
            'spread': None,
            'total': None,
            'home_implied_total': None,
            'away_implied_total': None
        }
        
        # Extract spreads and totals from bookmakers
        if 'bookmakers' in game and game['bookmakers']:
            bookmaker = game['bookmakers'][0]  # Use first bookmaker
            
            for market in bookmaker.get('markets', []):
                if market['key'] == 'spreads':
                    # Find home team spread
                    for outcome in market['outcomes']:
                        if outcome['name'] == home_team:
                            game_data['spread'] = outcome['point']
                            break
                
                elif market['key'] == 'totals':
                    # Get the total points
                    for outcome in market['outcomes']:
                        if outcome['name'] == 'Over':
                            game_data['total'] = outcome['point']
                            break
        
        # Calculate implied team totals
        if game_data['spread'] is not None and game_data['total'] is not None:
            total = game_data['total']
            spread = game_data['spread']  # Positive means home team is favored
            
            # Implied totals: total/2 +/- spread/2
            game_data['home_implied_total'] = total / 2 + spread / 2
            game_data['away_implied_total'] = total / 2 - spread / 2
        
        games.append(game_data)
    
    df = pd.DataFrame(games)

    # Ensure all expected columns exist even for empty DataFrame
    expected_columns = ['home_team', 'away_team', 'commence_time', 'spread', 'total',
                       'home_implied_total', 'away_implied_total']
    for col in expected_columns:
        if col not in df.columns:
            df[col] = None

    return df[expected_columns]


def fetch_secure_data(url: str) -> Dict[str, Any]:
    """
    Fetch data from a provider that requires basic authentication.

    Uses API_USERNAME and API_PASSWORD from environment variables.
    Designed for providers like Meteomatics Weather API.

    Args:
        url: The API endpoint URL

    Returns:
        Dictionary containing the JSON response

    Raises:
        ValueError: If credentials are not found in environment
        requests.HTTPError: If the request fails
    """
    api_username = os.getenv("API_USERNAME")
    api_password = os.getenv("API_PASSWORD")

    if not api_username or not api_password:
        raise ValueError("API_USERNAME and API_PASSWORD must be set in environment variables")

    try:
        response = requests.get(
            url,
            auth=HTTPBasicAuth(api_username, api_password),
            timeout=30
        )
        response.raise_for_status()
        return response.json()
    except Exception as e:
        raise Exception(f"Failed to fetch data from {url}: {e}")


def get_player_props(api_key: str, event_id: str, markets: List[str] = None,
                    sport: str = "americanfootball_nfl") -> Dict[str, Any]:
    """
    Fetch NFL player props from The Odds API for a specific event.

    Args:
        api_key: The Odds API key
        event_id: Specific event ID to get props for
        markets: List of player prop markets to fetch. If None, fetches all available.
        sport: Sport identifier (default: americanfootball_nfl)

    Returns:
        Dictionary containing player props data
    """
    url = f"https://api.the-odds-api.com/v4/sports/{sport}/events/{event_id}/odds"

    # Default to comprehensive player props if none specified
    if markets is None:
        markets = [
            # Core NFL player props
            'player_pass_yds', 'player_pass_tds', 'player_pass_completions', 'player_pass_attempts',
            'player_pass_interceptions', 'player_rush_yds', 'player_rush_tds', 'player_rush_attempts',
            'player_receptions', 'player_reception_yds', 'player_reception_tds',
            'player_rush_reception_yds', 'player_pass_rush_reception_yds',
            'player_anytime_td', 'player_1st_td', 'player_kicking_points',
            'player_tackles_assists', 'player_sacks', 'player_defensive_interceptions',
            # Alternate lines for key props
            'player_pass_yds_alternate', 'player_rush_yds_alternate', 'player_reception_yds_alternate',
            'player_receptions_alternate', 'player_pass_tds_alternate', 'player_rush_tds_alternate'
        ]

    params = {
        'api_key': api_key,
        'regions': 'us',
        'markets': ','.join(markets),
        'oddsFormat': 'american',
        'dateFormat': 'iso'
    }

    response = requests.get(url, params=params)
    response.raise_for_status()

    return response.json()


def get_advanced_game_markets(api_key: str, event_id: str,
                             sport: str = "americanfootball_nfl") -> Dict[str, Any]:
    """
    Fetch advanced game markets for enhanced game script analysis.

    Args:
        api_key: The Odds API key
        event_id: Specific event ID
        sport: Sport identifier

    Returns:
        Dictionary containing advanced market data
    """
    url = f"https://api.the-odds-api.com/v4/sports/{sport}/events/{event_id}/odds"

    # Advanced markets for game script simulation
    advanced_markets = [
        # Team totals and alternate lines
        'team_totals', 'alternate_spreads', 'alternate_totals',
        # Quarter/half markets
        'h2h_q1', 'h2h_h1', 'totals_q1', 'totals_h1',
        # Scoring markets
        'btts', 'highest_scoring_half', 'total_field_goals',
        # Game flow markets
        'first_touchdown_scorer', 'last_touchdown_scorer'
    ]

    params = {
        'api_key': api_key,
        'regions': 'us',
        'markets': ','.join(advanced_markets),
        'oddsFormat': 'american',
        'dateFormat': 'iso'
    }

    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f"Advanced markets not available: {e}")
        return {}


def find_game_event_id(api_key: str, home_team: str, away_team: str,
                      sport: str = "americanfootball_nfl") -> str:
    """
    Find the event ID for a specific NFL game.

    Args:
        api_key: The Odds API key
        home_team: Home team name (e.g., "Green Bay Packers")
        away_team: Away team name (e.g., "Washington Commanders")
        sport: Sport identifier

    Returns:
        Event ID string, or None if not found
    """
    # First get all current games
    odds_data = get_totals_spreads(api_key, sport)

    # Normalize team names for matching
    home_normalized = home_team.lower().replace(' ', '')
    away_normalized = away_team.lower().replace(' ', '')

    for game in odds_data:
        game_home = game.get('home_team', '').lower().replace(' ', '')
        game_away = game.get('away_team', '').lower().replace(' ', '')

        # Check for exact match or partial match
        if ((home_normalized in game_home or game_home in home_normalized) and
            (away_normalized in game_away or game_away in game_away)):
            return game.get('id')

        # Also check reverse (in case home/away are swapped)
        if ((away_normalized in game_home or game_home in away_normalized) and
            (home_normalized in game_away or game_away in home_normalized)):
            return game.get('id')

    return None


def process_player_props_response(props_data: Dict[str, Any]) -> pd.DataFrame:
    """
    Process raw player props API response into standardized DataFrame.

    Args:
        props_data: Raw response from player props API

    Returns:
        DataFrame with columns: player_name, market, line, over_odds, under_odds,
                               bookmaker, timestamp
    """
    processed_props = []

    if not props_data or 'bookmakers' not in props_data:
        return pd.DataFrame()

    for bookmaker in props_data['bookmakers']:
        book_name = bookmaker.get('title', 'Unknown')

        for market in bookmaker.get('markets', []):
            market_key = market.get('key', '')

            # Group outcomes by player for over/under pairs
            player_outcomes = {}

            for outcome in market.get('outcomes', []):
                player_name = outcome.get('description', '')
                outcome_name = outcome.get('name', '')
                price = outcome.get('price', 0)
                point = outcome.get('point')

                if player_name not in player_outcomes:
                    player_outcomes[player_name] = {}

                player_outcomes[player_name][outcome_name] = {
                    'price': price,
                    'point': point
                }

            # Create prop records for each player
            for player_name, outcomes in player_outcomes.items():
                if 'Over' in outcomes and 'Under' in outcomes:
                    over_data = outcomes['Over']
                    under_data = outcomes['Under']

                    # Use the point value (should be same for over/under)
                    line = over_data.get('point') or under_data.get('point')

                    if line is not None:
                        processed_props.append({
                            'player_name': player_name,
                            'market': market_key,
                            'line': float(line),
                            'over_odds': int(over_data['price']),
                            'under_odds': int(under_data['price']),
                            'bookmaker': book_name,
                            'timestamp': props_data.get('commence_time', '')
                        })

    return pd.DataFrame(processed_props)


def fetch_weather_data(lat: float, lon: float, date: str) -> Dict[str, Any]:
    """
    Fetch weather data from Meteomatics API for a specific location and date.

    Args:
        lat: Latitude
        lon: Longitude
        date: Date in ISO format (YYYY-MM-DD)

    Returns:
        Dictionary containing weather data
    """
    # Example Meteomatics API URL format
    # https://api.meteomatics.com/2024-01-15T12:00:00Z/t_2m:C,wind_speed_10m:ms,precip_1h:mm/40.7589,-73.9851/json

    url = f"https://api.meteomatics.com/{date}T12:00:00Z/t_2m:C,wind_speed_10m:ms,precip_1h:mm/{lat},{lon}/json"

    return fetch_secure_data(url)
