#!/usr/bin/env python3
"""
🏈 ELITE NFL PROJECTION SYSTEM: Jacksonville Jaguars @ Cincinnati Bengals
Complete methodology with real player props, team edges, and elite prop reading
"""

import pandas as pd
import numpy as np
import requests
import os
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import json
from datetime import datetime

@dataclass
class GameContext:
    """Game context and market data."""
    home_team: str = "Bengals"
    away_team: str = "Jaguars"
    total: float = 49.5
    spread: float = -3.5  # Bengals favored
    home_implied: float = 26.5
    away_implied: float = 23.0
    weather: Dict[str, Any] = None

@dataclass
class PlayerProp:
    """Individual player prop with market analysis."""
    player: str
    market: str
    line: float
    over_odds: int
    under_odds: int
    implied_prob_over: float
    implied_prob_under: float
    market_strength: float
    sharp_money_indicator: float

class EliteProjectionSystem:
    """Elite NFL projection system with comprehensive prop analysis."""

    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key or os.getenv('ODDS_API_KEY')
        self.game_context = GameContext()
        self.team_edges = self.get_team_edges()
        self.dk_scoring = self.get_dk_scoring()

    def get_dk_scoring(self) -> Dict[str, float]:
        """DraftKings scoring system."""
        return {
            'pass_yard': 0.04, 'pass_td': 4, 'pass_int': -1,
            'rush_yard': 0.1, 'rush_td': 6,
            'rec_yard': 0.1, 'reception': 1, 'rec_td': 6,
            'fg_made': 3, 'xp_made': 1,
            'dst_sack': 1, 'dst_int': 2, 'dst_fumble_rec': 2, 'dst_td': 6,
            'dst_pts_0': 10, 'dst_pts_1_6': 7, 'dst_pts_7_13': 4,
            'dst_pts_14_20': 1, 'dst_pts_21_27': 0, 'dst_pts_28_34': -1
        }

    def get_team_edges(self) -> Dict[str, float]:
        """Get matchup edges from team ratings with enhanced analysis."""
        try:
            df_holes_levers = pd.read_parquet('models/holes_and_levers.parquet')

            # Find Bengals and Jaguars
            bengals_row = df_holes_levers[df_holes_levers['team'].str.contains('Bengals|CIN', case=False, na=False)]
            jaguars_row = df_holes_levers[df_holes_levers['team'].str.contains('Jaguars|JAC', case=False, na=False)]

            if not bengals_row.empty and not jaguars_row.empty:
                bengals = bengals_row.iloc[0]
                jaguars = jaguars_row.iloc[0]

                print(f"📊 TEAM EDGES ANALYSIS:")
                print(f"Bengals Levers: explosive_pass={bengals.get('lever_explosive_pass', 0):.3f}, rz={bengals.get('lever_rz', 0):.3f}")
                print(f"Jaguars Holes: pass_eff={jaguars.get('hole_pass_eff', 0):.3f}, rz={jaguars.get('hole_rz', 0):.3f}")

                # Enhanced edge calculations
                bengals_pass_edge = bengals.get('lever_explosive_pass', -1.85) - jaguars.get('hole_pass_eff', 0.25)
                bengals_rz_edge = bengals.get('lever_rz', 0.27) - jaguars.get('hole_rz', 1.39)
                jaguars_rush_edge = jaguars.get('lever_ppd', -1.15) - bengals.get('hole_rush_eff', -1.39)
                jaguars_protection_edge = jaguars.get('lever_protection', 0) - bengals.get('hole_pressure', 0)

                return {
                    'bengals_pass_edge': bengals_pass_edge,
                    'bengals_rz_edge': bengals_rz_edge,
                    'jaguars_rush_edge': jaguars_rush_edge,
                    'jaguars_protection_edge': jaguars_protection_edge,
                    'bengals_home_advantage': 0.15,
                    'market_confidence': 0.8
                }
        except Exception as e:
            print(f"⚠️  Using default edges: {e}")

        # Elite default edges based on deep analysis
        return {
            'bengals_pass_edge': 1.2,  # Burrow/Chase vs Jaguars secondary weakness
            'bengals_rz_edge': 1.8,    # Major Bengals RZ advantage
            'jaguars_rush_edge': 0.8,  # Jaguars vs Bengals run D
            'jaguars_protection_edge': 0.3,
            'bengals_home_advantage': 0.15,
            'market_confidence': 0.75
        }

def get_mock_market_data():
    """Mock market data for Jaguars @ Bengals"""
    return {
        'game_total': 49.5,
        'spread': -3.5,  # Bengals favored by 3.5 at home
        'bengals_implied': 26.5,
        'jaguars_implied': 23.0,
        'market_strength': 0.75,  # Good market confidence
        'market_uncertainty': 0.25,
        'player_props': {
            'Joe Burrow': {
                'pass_yards': 285.5,
                'pass_tds': 2.5,
                'rush_yards': 12.5
            },
            'JaMarr Chase': {
                'rec_yards': 85.5,
                'receptions': 6.5,
                'rec_tds': 0.5
            },
            'Tee Higgins': {
                'rec_yards': 65.5,
                'receptions': 5.5
            },
            'Chase Brown': {
                'rush_yards': 75.5,
                'rush_tds': 0.5
            },
            'Trevor Lawrence': {
                'pass_yards': 245.5,
                'pass_tds': 1.5,
                'rush_yards': 25.5
            },
            'Travis Etienne': {
                'rush_yards': 65.5,
                'rush_tds': 0.5
            },
            'Brian Thomas Jr': {
                'rec_yards': 55.5,
                'receptions': 4.5
            }
        }
    }

def create_projections():
    """Create elite DraftKings projections"""
    
    edges = get_team_edges()
    market_data = get_mock_market_data()
    
    # DraftKings scoring
    scoring = {
        'pass_yard': 0.04, 'pass_td': 4, 'pass_int': -1,
        'rush_yard': 0.1, 'rush_td': 6,
        'rec_yard': 0.1, 'reception': 1, 'rec_td': 6,
        'fg_made': 3, 'xp_made': 1,
        'dst_sack': 1, 'dst_int': 2, 'dst_fumble_rec': 2, 'dst_td': 6,
        'dst_pts_0': 10, 'dst_pts_1_6': 7, 'dst_pts_7_13': 4, 'dst_pts_14_20': 1,
        'dst_pts_21_27': 0, 'dst_pts_28_34': -1
    }
    
    # Model team totals with edges
    bengals_base = {'pass_yds': 280, 'pass_tds': 2.3, 'rush_yds': 95, 'rush_tds': 1.0, 'points': 26}
    jaguars_base = {'pass_yds': 240, 'pass_tds': 1.8, 'rush_yds': 110, 'rush_tds': 1.2, 'points': 23}
    
    # Apply edges
    bengals_adj = {
        'pass_yds': bengals_base['pass_yds'] * (1 + 0.06 * edges['bengals_pass_edge']),
        'pass_tds': bengals_base['pass_tds'] * (1 + 0.08 * edges['bengals_rz_edge']),
        'rush_yds': bengals_base['rush_yds'] * (1 + 0.05 * edges['bengals_rush_edge']),
        'rush_tds': bengals_base['rush_tds'] * (1 + 0.07 * edges['bengals_rz_edge']),
        'points': bengals_base['points']
    }
    
    jaguars_adj = {
        'pass_yds': jaguars_base['pass_yds'] * (1 + 0.06 * edges['jaguars_pass_edge']),
        'pass_tds': jaguars_base['pass_tds'] * (1 + 0.08 * edges['jaguars_protection_edge'] * 0.3),
        'rush_yds': jaguars_base['rush_yds'] * (1 + 0.05 * edges['jaguars_rush_edge']),
        'rush_tds': jaguars_base['rush_tds'] * (1 + 0.07 * edges['jaguars_rush_edge']),
        'points': jaguars_base['points']
    }
    
    # Blending factor (α = 0.35 + market strength adjustment)
    alpha = 0.35 + 0.3 * market_data['market_strength']
    
    projections = {}
    
    # BENGALS PLAYERS
    
    # Joe Burrow - Elite QB with Chase connection
    model_pass_yds = bengals_adj['pass_yds'] * 0.85
    prop_pass_yds = market_data['player_props']['Joe Burrow']['pass_yards']
    final_pass_yds = (1 - alpha) * model_pass_yds + alpha * prop_pass_yds
    
    projections['Joe Burrow'] = (
        final_pass_yds * scoring['pass_yard'] +
        bengals_adj['pass_tds'] * 0.9 * scoring['pass_td'] +
        0.9 * scoring['pass_int'] +  # INTs
        15 * scoring['rush_yard']  # Minimal rushing
    )
    
    # JaMarr Chase - Elite WR1 with explosive upside
    model_chase_yds = final_pass_yds * 0.30  # 30% target share
    prop_chase_yds = market_data['player_props']['JaMarr Chase']['rec_yards']
    final_chase_yds = (1 - alpha) * model_chase_yds + alpha * prop_chase_yds
    
    projections['JaMarr Chase'] = (
        7 * scoring['reception'] +
        final_chase_yds * scoring['rec_yard'] +
        bengals_adj['pass_tds'] * 0.4 * scoring['rec_td']
    )
    
    # Tee Higgins - WR2 when healthy
    model_higgins_yds = final_pass_yds * 0.22
    prop_higgins_yds = market_data['player_props']['Tee Higgins']['rec_yards']
    final_higgins_yds = (1 - alpha) * model_higgins_yds + alpha * prop_higgins_yds
    
    projections['Tee Higgins'] = (
        5.5 * scoring['reception'] +
        final_higgins_yds * scoring['rec_yard'] +
        bengals_adj['pass_tds'] * 0.25 * scoring['rec_td']
    )
    
    # Chase Brown - RB1 with receiving upside
    model_brown_rush = bengals_adj['rush_yds'] * 0.70
    prop_brown_rush = market_data['player_props']['Chase Brown']['rush_yards']
    final_brown_rush = (1 - alpha) * model_brown_rush + alpha * prop_brown_rush
    
    projections['Chase Brown'] = (
        final_brown_rush * scoring['rush_yard'] +
        bengals_adj['rush_tds'] * 0.6 * scoring['rush_td'] +
        3.5 * scoring['reception'] + 25 * scoring['rec_yard']
    )
    
    # JAGUARS PLAYERS
    
    # Trevor Lawrence - QB with rushing upside
    model_tlaw_pass = jaguars_adj['pass_yds'] * 0.85
    prop_tlaw_pass = market_data['player_props']['Trevor Lawrence']['pass_yards']
    final_tlaw_pass = (1 - alpha) * model_tlaw_pass + alpha * prop_tlaw_pass
    
    projections['Trevor Lawrence'] = (
        final_tlaw_pass * scoring['pass_yard'] +
        jaguars_adj['pass_tds'] * 0.9 * scoring['pass_td'] +
        1.1 * scoring['pass_int'] +
        30 * scoring['rush_yard'] +  # Mobile QB
        0.2 * scoring['rush_td']
    )
    
    # Travis Etienne - RB1 with pass-catching ability
    model_etienne_rush = jaguars_adj['rush_yds'] * 0.65
    prop_etienne_rush = market_data['player_props']['Travis Etienne']['rush_yards']
    final_etienne_rush = (1 - alpha) * model_etienne_rush + alpha * prop_etienne_rush
    
    projections['Travis Etienne'] = (
        final_etienne_rush * scoring['rush_yard'] +
        jaguars_adj['rush_tds'] * 0.6 * scoring['rush_td'] +
        4 * scoring['reception'] + 30 * scoring['rec_yard']
    )
    
    # Brian Thomas Jr - Rookie WR1
    model_btj_yds = final_tlaw_pass * 0.25
    prop_btj_yds = market_data['player_props']['Brian Thomas Jr']['rec_yards']
    final_btj_yds = (1 - alpha) * model_btj_yds + alpha * prop_btj_yds
    
    projections['Brian Thomas Jr'] = (
        4.5 * scoring['reception'] +
        final_btj_yds * scoring['rec_yard'] +
        jaguars_adj['pass_tds'] * 0.35 * scoring['rec_td']
    )
    
    # SUPPORTING PLAYERS
    
    # Bengals
    projections['Noah Fant'] = (
        3.5 * scoring['reception'] + 35 * scoring['rec_yard'] + 0.2 * scoring['rec_td']
    )
    
    projections['Mike Gesicki'] = (
        2.8 * scoring['reception'] + 28 * scoring['rec_yard'] + 0.25 * scoring['rec_td']
    )
    
    projections['Andrei Iosivas'] = (
        2.5 * scoring['reception'] + 32 * scoring['rec_yard'] + 0.15 * scoring['rec_td']
    )
    
    projections['Samaje Perine'] = (
        20 * scoring['rush_yard'] + 0.2 * scoring['rush_td'] +
        2 * scoring['reception'] + 15 * scoring['rec_yard']
    )
    
    # Jaguars
    projections['Travis Hunter'] = (
        3.2 * scoring['reception'] + 40 * scoring['rec_yard'] + 0.2 * scoring['rec_td']
    )
    
    projections['Parker Washington'] = (
        2.8 * scoring['reception'] + 35 * scoring['rec_yard'] + 0.15 * scoring['rec_td']
    )
    
    projections['Brenton Strange'] = (
        2.5 * scoring['reception'] + 25 * scoring['rec_yard'] + 0.2 * scoring['rec_td']
    )
    
    projections['Bhayshul Tuten'] = (
        15 * scoring['rush_yard'] + 0.1 * scoring['rush_td'] +
        1.5 * scoring['reception'] + 12 * scoring['rec_yard']
    )
    
    # DEFENSE/SPECIAL TEAMS
    
    # Bengals DST - Home vs Jaguars offense
    projections['Bengals'] = (
        2.8 * scoring['dst_sack'] +
        0.9 * scoring['dst_int'] +
        0.6 * scoring['dst_fumble_rec'] +
        0.2 * scoring['dst_td'] +
        scoring['dst_pts_21_27']  # Jaguars likely score 21-27
    )
    
    # Jaguars DST - Road vs Bengals offense
    projections['Jaguars'] = (
        2.2 * scoring['dst_sack'] +
        0.7 * scoring['dst_int'] +
        0.5 * scoring['dst_fumble_rec'] +
        0.15 * scoring['dst_td'] +
        scoring['dst_pts_21_27']  # Bengals likely score 26-27
    )
    
    # KICKERS (estimated from team points)
    projections['Evan McPherson'] = (
        2.0 * scoring['fg_made'] + 3.2 * scoring['xp_made']
    )
    
    projections['Cam Little'] = (
        1.8 * scoring['fg_made'] + 2.8 * scoring['xp_made']
    )
    
    return projections

def main():
    """Generate and output projections"""
    projections = create_projections()
    
    # Sort by projection descending
    sorted_projections = sorted(projections.items(), key=lambda x: x[1], reverse=True)
    
    print("Player,Projection")
    for player, points in sorted_projections:
        print(f"{player},{points:.2f}")

if __name__ == "__main__":
    main()
