#!/usr/bin/env python3
"""
🏈 ELITE NFL PROJECTION SYSTEM: Jacksonville Jaguars @ Cincinnati Bengals
Complete methodology with real player props, team edges, and elite prop reading
"""

import pandas as pd
import numpy as np
import requests
import os
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import json
from datetime import datetime

@dataclass
class GameContext:
    """Game context and market data."""
    home_team: str = "Bengals"
    away_team: str = "Jaguars"
    total: float = 49.5
    spread: float = -3.5  # Bengals favored
    home_implied: float = 26.5
    away_implied: float = 23.0
    weather: Dict[str, Any] = None

@dataclass
class PlayerProp:
    """Individual player prop with market analysis."""
    player: str
    market: str
    line: float
    over_odds: int
    under_odds: int
    implied_prob_over: float
    implied_prob_under: float
    market_strength: float
    sharp_money_indicator: float

class EliteProjectionSystem:
    """Elite NFL projection system with comprehensive prop analysis."""

    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key or os.getenv('ODDS_API_KEY')
        self.game_context = GameContext()
        self.team_edges = self.get_team_edges()
        self.dk_scoring = self.get_dk_scoring()

    def get_dk_scoring(self) -> Dict[str, float]:
        """DraftKings scoring system."""
        return {
            'pass_yard': 0.04, 'pass_td': 4, 'pass_int': -1,
            'rush_yard': 0.1, 'rush_td': 6,
            'rec_yard': 0.1, 'reception': 1, 'rec_td': 6,
            'fg_made': 3, 'xp_made': 1,
            'dst_sack': 1, 'dst_int': 2, 'dst_fumble_rec': 2, 'dst_td': 6,
            'dst_pts_0': 10, 'dst_pts_1_6': 7, 'dst_pts_7_13': 4,
            'dst_pts_14_20': 1, 'dst_pts_21_27': 0, 'dst_pts_28_34': -1
        }

    def get_team_edges(self) -> Dict[str, float]:
        """Get matchup edges from team ratings with enhanced analysis."""
        try:
            df_holes_levers = pd.read_parquet('models/holes_and_levers.parquet')

            # Find Bengals and Jaguars
            bengals_row = df_holes_levers[df_holes_levers['team'].str.contains('Bengals|CIN', case=False, na=False)]
            jaguars_row = df_holes_levers[df_holes_levers['team'].str.contains('Jaguars|JAC', case=False, na=False)]

            if not bengals_row.empty and not jaguars_row.empty:
                bengals = bengals_row.iloc[0]
                jaguars = jaguars_row.iloc[0]

                print(f"📊 TEAM EDGES ANALYSIS:")
                print(f"Bengals Levers: explosive_pass={bengals.get('lever_explosive_pass', 0):.3f}, rz={bengals.get('lever_rz', 0):.3f}")
                print(f"Jaguars Holes: pass_eff={jaguars.get('hole_pass_eff', 0):.3f}, rz={jaguars.get('hole_rz', 0):.3f}")

                # Enhanced edge calculations
                bengals_pass_edge = bengals.get('lever_explosive_pass', -1.85) - jaguars.get('hole_pass_eff', 0.25)
                bengals_rz_edge = bengals.get('lever_rz', 0.27) - jaguars.get('hole_rz', 1.39)
                jaguars_rush_edge = jaguars.get('lever_ppd', -1.15) - bengals.get('hole_rush_eff', -1.39)
                jaguars_protection_edge = jaguars.get('lever_protection', 0) - bengals.get('hole_pressure', 0)

                return {
                    'bengals_pass_edge': bengals_pass_edge,
                    'bengals_rz_edge': bengals_rz_edge,
                    'jaguars_rush_edge': jaguars_rush_edge,
                    'jaguars_protection_edge': jaguars_protection_edge,
                    'bengals_home_advantage': 0.15,
                    'market_confidence': 0.8
                }
        except Exception as e:
            print(f"⚠️  Using default edges: {e}")

        # Elite default edges based on deep analysis
        return {
            'bengals_pass_edge': 1.2,  # Burrow/Chase vs Jaguars secondary weakness
            'bengals_rz_edge': 1.8,    # Major Bengals RZ advantage
            'jaguars_rush_edge': 0.8,  # Jaguars vs Bengals run D
            'jaguars_protection_edge': 0.3,
            'bengals_home_advantage': 0.15,
            'market_confidence': 0.75
        }

    def fetch_comprehensive_props(self) -> Dict[str, Any]:
        """Fetch comprehensive player props with sharp book prioritization."""
        if not self.api_key:
            print("⚠️  No API key found. Using mock data for demonstration.")
            return self.get_mock_market_data()

        try:
            # Priority hierarchy: Pinnacle > Circa > FanDuel > DraftKings > Others
            sharp_books = ['pinnacle', 'circa', 'fanduel', 'draftkings', 'betmgm', 'caesars']

            # Comprehensive prop markets (20+ markets as requested)
            prop_markets = [
                'player_pass_yds', 'player_pass_tds', 'player_pass_completions', 'player_pass_attempts',
                'player_pass_interceptions', 'player_rush_yds', 'player_rush_tds', 'player_rush_attempts',
                'player_receptions', 'player_reception_yds', 'player_reception_tds',
                'player_rush_reception_yds', 'player_anytime_td', 'player_1st_td',
                'player_tackles_assists', 'player_sacks', 'player_defensive_interceptions',
                'player_kicking_points', 'player_pass_yds_alternate', 'player_rush_yds_alternate',
                'player_reception_yds_alternate', 'player_receptions_alternate', 'player_longest_reception',
                'player_longest_rush', 'player_longest_pass', 'player_fumbles_lost'
            ]

            # Get JAX @ CIN game
            games_url = f"https://api.the-odds-api.com/v4/sports/americanfootball_nfl/odds"
            games_params = {
                'api_key': self.api_key,
                'regions': 'us',
                'markets': 'spreads,totals',
                'oddsFormat': 'american'
            }

            games_response = requests.get(games_url, params=games_params)
            games_response.raise_for_status()
            games_data = games_response.json()

            # Find JAX @ CIN game
            target_game = None
            for game in games_data:
                teams = [game['home_team'], game['away_team']]
                if any('Jaguars' in team or 'Jacksonville' in team for team in teams) and \
                   any('Bengals' in team or 'Cincinnati' in team for team in teams):
                    target_game = game
                    break

            if not target_game:
                print("⚠️  JAX @ CIN game not found. Using mock data.")
                return self.get_mock_market_data()

            # Fetch player props for the game
            props_url = f"https://api.the-odds-api.com/v4/sports/americanfootball_nfl/events/{target_game['id']}/odds"
            props_params = {
                'api_key': self.api_key,
                'regions': 'us',
                'markets': ','.join(prop_markets),
                'oddsFormat': 'american'
            }

            props_response = requests.get(props_url, params=props_params)
            props_response.raise_for_status()
            props_data = props_response.json()

            return self.process_sharp_props(props_data, sharp_books)

        except Exception as e:
            print(f"⚠️  API fetch failed: {e}. Using mock data.")
            return self.get_mock_market_data()

    def process_sharp_props(self, props_data: Dict[str, Any], sharp_books: List[str]) -> Dict[str, Any]:
        """Process props with sharp book prioritization and market analysis."""
        processed_props = {}

        # Extract bookmaker data with sharp prioritization
        bookmaker_hierarchy = {}
        for bookmaker in props_data.get('bookmakers', []):
            book_key = bookmaker['key'].lower()
            if book_key in sharp_books:
                bookmaker_hierarchy[book_key] = {
                    'priority': sharp_books.index(book_key),
                    'data': bookmaker
                }

        # Process each market with elite prop reading methodology
        for market in props_data.get('bookmakers', [{}])[0].get('markets', []):
            market_key = market['key']

            for outcome in market.get('outcomes', []):
                player_name = outcome.get('description', '').replace(' (', '').replace(')', '')

                if player_name not in processed_props:
                    processed_props[player_name] = {}

                # Get best sharp line (prioritize Pinnacle/Circa)
                sharp_line = self.get_sharp_line(market_key, player_name, bookmaker_hierarchy)
                if sharp_line:
                    processed_props[player_name][market_key] = sharp_line

        return {
            'game_context': self.game_context,
            'player_props': processed_props,
            'market_analysis': self.analyze_market_strength(bookmaker_hierarchy)
        }

    def get_sharp_line(self, market: str, player: str, bookmaker_hierarchy: Dict) -> Optional[Dict]:
        """Get the sharpest available line for a player prop."""
        # Priority: Pinnacle > Circa > Others
        for book_key in ['pinnacle', 'circa', 'fanduel', 'draftkings']:
            if book_key in bookmaker_hierarchy:
                book_data = bookmaker_hierarchy[book_key]['data']
                for market_data in book_data.get('markets', []):
                    if market_data['key'] == market:
                        for outcome in market_data.get('outcomes', []):
                            if player in outcome.get('description', ''):
                                return {
                                    'line': outcome.get('point', 0),
                                    'over_odds': outcome.get('price', 0) if outcome.get('name') == 'Over' else None,
                                    'under_odds': outcome.get('price', 0) if outcome.get('name') == 'Under' else None,
                                    'book': book_key,
                                    'market_strength': self.calculate_market_strength(outcome)
                                }
        return None

    def calculate_market_strength(self, outcome: Dict) -> float:
        """Calculate market strength based on odds and book reputation."""
        price = abs(outcome.get('price', -110))

        # Stronger markets have tighter lines (closer to -110)
        if price <= 105:
            return 0.95  # Very strong
        elif price <= 115:
            return 0.85  # Strong
        elif price <= 125:
            return 0.70  # Moderate
        else:
            return 0.50  # Weak

    def analyze_market_strength(self, bookmaker_hierarchy: Dict) -> Dict[str, float]:
        """Analyze overall market strength and sharp money indicators."""
        return {
            'overall_strength': 0.85 if 'pinnacle' in bookmaker_hierarchy else 0.70,
            'sharp_book_count': len([b for b in bookmaker_hierarchy.keys() if b in ['pinnacle', 'circa']]),
            'market_confidence': 0.80,
            'line_movement_factor': 1.0  # Would track line movement in full implementation
        }

    def get_mock_market_data(self) -> Dict[str, Any]:
        """Enhanced mock data with sharp book simulation for demonstration."""
        return {
            'game_context': self.game_context,
            'market_analysis': {
                'overall_strength': 0.85,
                'sharp_book_count': 2,
                'market_confidence': 0.80,
                'line_movement_factor': 1.0
            },
            'player_props': {
                'Joe Burrow': {
                    'player_pass_yds': {'line': 285.5, 'over_odds': -110, 'under_odds': -110, 'book': 'pinnacle', 'market_strength': 0.95},
                    'player_pass_tds': {'line': 2.5, 'over_odds': -105, 'under_odds': -115, 'book': 'pinnacle', 'market_strength': 0.90},
                    'player_pass_completions': {'line': 24.5, 'over_odds': -110, 'under_odds': -110, 'book': 'circa', 'market_strength': 0.85},
                    'player_anytime_td': {'line': 0.5, 'over_odds': +450, 'under_odds': -650, 'book': 'pinnacle', 'market_strength': 0.80}
                },
                'JaMarr Chase': {
                    'player_reception_yds': {'line': 85.5, 'over_odds': -108, 'under_odds': -112, 'book': 'pinnacle', 'market_strength': 0.95},
                    'player_receptions': {'line': 6.5, 'over_odds': -115, 'under_odds': -105, 'book': 'pinnacle', 'market_strength': 0.90},
                    'player_anytime_td': {'line': 0.5, 'over_odds': +140, 'under_odds': -180, 'book': 'pinnacle', 'market_strength': 0.85},
                    'player_longest_reception': {'line': 22.5, 'over_odds': -110, 'under_odds': -110, 'book': 'circa', 'market_strength': 0.75}
                },
                'Tee Higgins': {
                    'player_reception_yds': {'line': 65.5, 'over_odds': -110, 'under_odds': -110, 'book': 'pinnacle', 'market_strength': 0.90},
                    'player_receptions': {'line': 5.5, 'over_odds': -105, 'under_odds': -115, 'book': 'circa', 'market_strength': 0.85},
                    'player_anytime_td': {'line': 0.5, 'over_odds': +180, 'under_odds': -240, 'book': 'pinnacle', 'market_strength': 0.80}
                },
                'Chase Brown': {
                    'player_rush_yds': {'line': 75.5, 'over_odds': -110, 'under_odds': -110, 'book': 'pinnacle', 'market_strength': 0.90},
                    'player_rush_attempts': {'line': 16.5, 'over_odds': -115, 'under_odds': -105, 'book': 'circa', 'market_strength': 0.85},
                    'player_anytime_td': {'line': 0.5, 'over_odds': +160, 'under_odds': -200, 'book': 'pinnacle', 'market_strength': 0.85}
                },
                'Trevor Lawrence': {
                    'player_pass_yds': {'line': 245.5, 'over_odds': -110, 'under_odds': -110, 'book': 'pinnacle', 'market_strength': 0.90},
                    'player_pass_tds': {'line': 1.5, 'over_odds': -120, 'under_odds': +100, 'book': 'pinnacle', 'market_strength': 0.85},
                    'player_rush_yds': {'line': 25.5, 'over_odds': -105, 'under_odds': -115, 'book': 'circa', 'market_strength': 0.80},
                    'player_anytime_td': {'line': 0.5, 'over_odds': +500, 'under_odds': -750, 'book': 'pinnacle', 'market_strength': 0.75}
                },
                'Travis Etienne': {
                    'player_rush_yds': {'line': 65.5, 'over_odds': -110, 'under_odds': -110, 'book': 'pinnacle', 'market_strength': 0.90},
                    'player_rush_attempts': {'line': 14.5, 'over_odds': -105, 'under_odds': -115, 'book': 'circa', 'market_strength': 0.85},
                    'player_anytime_td': {'line': 0.5, 'over_odds': +170, 'under_odds': -220, 'book': 'pinnacle', 'market_strength': 0.85}
                },
                'Brian Thomas Jr': {
                    'player_reception_yds': {'line': 55.5, 'over_odds': -110, 'under_odds': -110, 'book': 'pinnacle', 'market_strength': 0.85},
                    'player_receptions': {'line': 4.5, 'over_odds': -115, 'under_odds': -105, 'book': 'circa', 'market_strength': 0.80},
                    'player_anytime_td': {'line': 0.5, 'over_odds': +200, 'under_odds': -260, 'book': 'pinnacle', 'market_strength': 0.80}
                }
            }
        }

def create_projections():
    """Create elite DraftKings projections"""
    
    edges = get_team_edges()
    market_data = get_mock_market_data()
    
    # DraftKings scoring
    scoring = {
        'pass_yard': 0.04, 'pass_td': 4, 'pass_int': -1,
        'rush_yard': 0.1, 'rush_td': 6,
        'rec_yard': 0.1, 'reception': 1, 'rec_td': 6,
        'fg_made': 3, 'xp_made': 1,
        'dst_sack': 1, 'dst_int': 2, 'dst_fumble_rec': 2, 'dst_td': 6,
        'dst_pts_0': 10, 'dst_pts_1_6': 7, 'dst_pts_7_13': 4, 'dst_pts_14_20': 1,
        'dst_pts_21_27': 0, 'dst_pts_28_34': -1
    }
    
    # Model team totals with edges
    bengals_base = {'pass_yds': 280, 'pass_tds': 2.3, 'rush_yds': 95, 'rush_tds': 1.0, 'points': 26}
    jaguars_base = {'pass_yds': 240, 'pass_tds': 1.8, 'rush_yds': 110, 'rush_tds': 1.2, 'points': 23}
    
    # Apply edges
    bengals_adj = {
        'pass_yds': bengals_base['pass_yds'] * (1 + 0.06 * edges['bengals_pass_edge']),
        'pass_tds': bengals_base['pass_tds'] * (1 + 0.08 * edges['bengals_rz_edge']),
        'rush_yds': bengals_base['rush_yds'] * (1 + 0.05 * edges['bengals_rush_edge']),
        'rush_tds': bengals_base['rush_tds'] * (1 + 0.07 * edges['bengals_rz_edge']),
        'points': bengals_base['points']
    }
    
    jaguars_adj = {
        'pass_yds': jaguars_base['pass_yds'] * (1 + 0.06 * edges['jaguars_pass_edge']),
        'pass_tds': jaguars_base['pass_tds'] * (1 + 0.08 * edges['jaguars_protection_edge'] * 0.3),
        'rush_yds': jaguars_base['rush_yds'] * (1 + 0.05 * edges['jaguars_rush_edge']),
        'rush_tds': jaguars_base['rush_tds'] * (1 + 0.07 * edges['jaguars_rush_edge']),
        'points': jaguars_base['points']
    }
    
    # Blending factor (α = 0.35 + market strength adjustment)
    alpha = 0.35 + 0.3 * market_data['market_strength']
    
    projections = {}
    
    # BENGALS PLAYERS
    
    # Joe Burrow - Elite QB with Chase connection
    model_pass_yds = bengals_adj['pass_yds'] * 0.85
    prop_pass_yds = market_data['player_props']['Joe Burrow']['pass_yards']
    final_pass_yds = (1 - alpha) * model_pass_yds + alpha * prop_pass_yds
    
    projections['Joe Burrow'] = (
        final_pass_yds * scoring['pass_yard'] +
        bengals_adj['pass_tds'] * 0.9 * scoring['pass_td'] +
        0.9 * scoring['pass_int'] +  # INTs
        15 * scoring['rush_yard']  # Minimal rushing
    )
    
    # JaMarr Chase - Elite WR1 with explosive upside
    model_chase_yds = final_pass_yds * 0.30  # 30% target share
    prop_chase_yds = market_data['player_props']['JaMarr Chase']['rec_yards']
    final_chase_yds = (1 - alpha) * model_chase_yds + alpha * prop_chase_yds
    
    projections['JaMarr Chase'] = (
        7 * scoring['reception'] +
        final_chase_yds * scoring['rec_yard'] +
        bengals_adj['pass_tds'] * 0.4 * scoring['rec_td']
    )
    
    # Tee Higgins - WR2 when healthy
    model_higgins_yds = final_pass_yds * 0.22
    prop_higgins_yds = market_data['player_props']['Tee Higgins']['rec_yards']
    final_higgins_yds = (1 - alpha) * model_higgins_yds + alpha * prop_higgins_yds
    
    projections['Tee Higgins'] = (
        5.5 * scoring['reception'] +
        final_higgins_yds * scoring['rec_yard'] +
        bengals_adj['pass_tds'] * 0.25 * scoring['rec_td']
    )
    
    # Chase Brown - RB1 with receiving upside
    model_brown_rush = bengals_adj['rush_yds'] * 0.70
    prop_brown_rush = market_data['player_props']['Chase Brown']['rush_yards']
    final_brown_rush = (1 - alpha) * model_brown_rush + alpha * prop_brown_rush
    
    projections['Chase Brown'] = (
        final_brown_rush * scoring['rush_yard'] +
        bengals_adj['rush_tds'] * 0.6 * scoring['rush_td'] +
        3.5 * scoring['reception'] + 25 * scoring['rec_yard']
    )
    
    # JAGUARS PLAYERS
    
    # Trevor Lawrence - QB with rushing upside
    model_tlaw_pass = jaguars_adj['pass_yds'] * 0.85
    prop_tlaw_pass = market_data['player_props']['Trevor Lawrence']['pass_yards']
    final_tlaw_pass = (1 - alpha) * model_tlaw_pass + alpha * prop_tlaw_pass
    
    projections['Trevor Lawrence'] = (
        final_tlaw_pass * scoring['pass_yard'] +
        jaguars_adj['pass_tds'] * 0.9 * scoring['pass_td'] +
        1.1 * scoring['pass_int'] +
        30 * scoring['rush_yard'] +  # Mobile QB
        0.2 * scoring['rush_td']
    )
    
    # Travis Etienne - RB1 with pass-catching ability
    model_etienne_rush = jaguars_adj['rush_yds'] * 0.65
    prop_etienne_rush = market_data['player_props']['Travis Etienne']['rush_yards']
    final_etienne_rush = (1 - alpha) * model_etienne_rush + alpha * prop_etienne_rush
    
    projections['Travis Etienne'] = (
        final_etienne_rush * scoring['rush_yard'] +
        jaguars_adj['rush_tds'] * 0.6 * scoring['rush_td'] +
        4 * scoring['reception'] + 30 * scoring['rec_yard']
    )
    
    # Brian Thomas Jr - Rookie WR1
    model_btj_yds = final_tlaw_pass * 0.25
    prop_btj_yds = market_data['player_props']['Brian Thomas Jr']['rec_yards']
    final_btj_yds = (1 - alpha) * model_btj_yds + alpha * prop_btj_yds
    
    projections['Brian Thomas Jr'] = (
        4.5 * scoring['reception'] +
        final_btj_yds * scoring['rec_yard'] +
        jaguars_adj['pass_tds'] * 0.35 * scoring['rec_td']
    )
    
    # SUPPORTING PLAYERS
    
    # Bengals
    projections['Noah Fant'] = (
        3.5 * scoring['reception'] + 35 * scoring['rec_yard'] + 0.2 * scoring['rec_td']
    )
    
    projections['Mike Gesicki'] = (
        2.8 * scoring['reception'] + 28 * scoring['rec_yard'] + 0.25 * scoring['rec_td']
    )
    
    projections['Andrei Iosivas'] = (
        2.5 * scoring['reception'] + 32 * scoring['rec_yard'] + 0.15 * scoring['rec_td']
    )
    
    projections['Samaje Perine'] = (
        20 * scoring['rush_yard'] + 0.2 * scoring['rush_td'] +
        2 * scoring['reception'] + 15 * scoring['rec_yard']
    )
    
    # Jaguars
    projections['Travis Hunter'] = (
        3.2 * scoring['reception'] + 40 * scoring['rec_yard'] + 0.2 * scoring['rec_td']
    )
    
    projections['Parker Washington'] = (
        2.8 * scoring['reception'] + 35 * scoring['rec_yard'] + 0.15 * scoring['rec_td']
    )
    
    projections['Brenton Strange'] = (
        2.5 * scoring['reception'] + 25 * scoring['rec_yard'] + 0.2 * scoring['rec_td']
    )
    
    projections['Bhayshul Tuten'] = (
        15 * scoring['rush_yard'] + 0.1 * scoring['rush_td'] +
        1.5 * scoring['reception'] + 12 * scoring['rec_yard']
    )
    
    # DEFENSE/SPECIAL TEAMS
    
    # Bengals DST - Home vs Jaguars offense
    projections['Bengals'] = (
        2.8 * scoring['dst_sack'] +
        0.9 * scoring['dst_int'] +
        0.6 * scoring['dst_fumble_rec'] +
        0.2 * scoring['dst_td'] +
        scoring['dst_pts_21_27']  # Jaguars likely score 21-27
    )
    
    # Jaguars DST - Road vs Bengals offense
    projections['Jaguars'] = (
        2.2 * scoring['dst_sack'] +
        0.7 * scoring['dst_int'] +
        0.5 * scoring['dst_fumble_rec'] +
        0.15 * scoring['dst_td'] +
        scoring['dst_pts_21_27']  # Bengals likely score 26-27
    )
    
    # KICKERS (estimated from team points)
    projections['Evan McPherson'] = (
        2.0 * scoring['fg_made'] + 3.2 * scoring['xp_made']
    )
    
    projections['Cam Little'] = (
        1.8 * scoring['fg_made'] + 2.8 * scoring['xp_made']
    )
    
    return projections

def main():
    """Generate and output projections"""
    projections = create_projections()
    
    # Sort by projection descending
    sorted_projections = sorted(projections.items(), key=lambda x: x[1], reverse=True)
    
    print("Player,Projection")
    for player, points in sorted_projections:
        print(f"{player},{points:.2f}")

if __name__ == "__main__":
    main()
